﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{0D69B020-3F2D-43D8-BDEA-3D28D07B06B0}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>HIH.Framework.AutoCrawingManager</RootNamespace>
    <AssemblyName>HIH.Framework.AutoCrawingManager</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="DevExpress.Data.Desktop.v20.2, Version=20.2.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Data.v20.2, Version=20.2.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Utils.v20.2, Version=20.2.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraBars.v20.2, Version=20.2.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraEditors.v20.2, Version=20.2.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraGrid.v20.2, Version=20.2.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraLayout.v20.2, Version=20.2.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="HIH.Framework.Base">
      <HintPath>..\..\..\..\weixinfile\WeChat Files\wxid_gkx1wq5j61nl22\FileStorage\File\2022-04\HIH.Framework-V3.2.1_Empty\HIH.Framework-V3.2.1_Empty\DLL\HIH.Framework.Base.dll</HintPath>
    </Reference>
    <Reference Include="HIH.Framework.BaseUIDX">
      <HintPath>..\..\..\..\weixinfile\WeChat Files\wxid_gkx1wq5j61nl22\FileStorage\File\2022-04\HIH.Framework-V3.2.1_Empty\HIH.Framework-V3.2.1_Empty\DLL\HIH.Framework.BaseUIDX.dll</HintPath>
    </Reference>
    <Reference Include="HIH.Framework.Common, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\HIHFramework\ImportDLL\HIH.Framework.Common.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Web.WebView2.Core, Version=1.0.2420.47, Culture=neutral, PublicKeyToken=2a8ab48044d2601e, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Web.WebView2.1.0.2420.47\lib\net45\Microsoft.Web.WebView2.Core.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Web.WebView2.WinForms, Version=1.0.2420.47, Culture=neutral, PublicKeyToken=2a8ab48044d2601e, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Web.WebView2.1.0.2420.47\lib\net45\Microsoft.Web.WebView2.WinForms.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Web.WebView2.Wpf, Version=1.0.2420.47, Culture=neutral, PublicKeyToken=2a8ab48044d2601e, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Web.WebView2.1.0.2420.47\lib\net45\Microsoft.Web.WebView2.Wpf.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="frmCrawingMenu.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmCrawingMenu.Designer.cs">
      <DependentUpon>frmCrawingMenu.cs</DependentUpon>
    </Compile>
    <Compile Include="frmETAEditForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmETAEditForm.Designer.cs">
      <DependentUpon>frmETAEditForm.cs</DependentUpon>
    </Compile>
    <Compile Include="frmOrderResultForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmOrderResultForm.Designer.cs">
      <DependentUpon>frmOrderResultForm.cs</DependentUpon>
    </Compile>
    <Compile Include="IImageHelper.cs" />
    <Compile Include="IProcess.cs" />
    <Compile Include="IProcessData.cs" />
    <Compile Include="IProcessItem.cs" />
    <Compile Include="IProcessType.cs" />
    <Compile Include="IResultType.cs" />
    <Compile Include="ISearchItem.cs" />
    <Compile Include="Process\IONE_MultiRow.cs" />
    <Compile Include="Process\ISSPH.cs" />
    <Compile Include="Process\IZIM.cs" />
    <Compile Include="Process\ICMA.cs" />
    <Compile Include="Process\ICOSCO.cs" />
    <Compile Include="Process\IEMC.cs" />
    <Compile Include="Process\IHAPAG.cs" />
    <Compile Include="Process\IHMM.cs" />
    <Compile Include="Process\IMSC.cs" />
    <Compile Include="Process\IMSK.cs" />
    <Compile Include="Process\IONE.cs" />
    <Compile Include="Process\IOOCL.cs" />
    <Compile Include="Process\IWHL.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="Result\IResult.cs" />
    <Compile Include="Result\IResultContainer.cs" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="frmCrawingMenu.resx">
      <DependentUpon>frmCrawingMenu.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmETAEditForm.resx">
      <DependentUpon>frmETAEditForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmOrderResultForm.resx">
      <DependentUpon>frmOrderResultForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\licenses.licx" />
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\cancel_normal.svg" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\cancel_active.svg" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Import Project="..\packages\Microsoft.Web.WebView2.1.0.2420.47\build\Microsoft.Web.WebView2.targets" Condition="Exists('..\packages\Microsoft.Web.WebView2.1.0.2420.47\build\Microsoft.Web.WebView2.targets')" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>这台计算机上缺少此项目引用的 NuGet 程序包。使用“NuGet 程序包还原”可下载这些程序包。有关更多信息，请参见 http://go.microsoft.com/fwlink/?LinkID=322105。缺少的文件是 {0}。</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\Microsoft.Web.WebView2.1.0.2420.47\build\Microsoft.Web.WebView2.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.Web.WebView2.1.0.2420.47\build\Microsoft.Web.WebView2.targets'))" />
  </Target>
</Project>