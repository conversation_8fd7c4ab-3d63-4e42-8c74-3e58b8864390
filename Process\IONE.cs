using HIH.Framework.AutoCrawingManager.Result;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace HIH.Framework.AutoCrawingManager.Process
{
    public class IONE : IProcess
    {
        private readonly string SearchMainPage = "https://ecomm.one-line.com/one-ecom";

        public IONE():base("ONE","ONEY"){}


        public override List<IProcessItem> Run(string searchKey,string replaceText)
        {
            List<IProcessItem> processList = new List<IProcessItem>();

            IProcessItem firPro = new IProcessItem(0, "跳转", IProcessType.JUMPTO);
            IProcessItem secPro = new IProcessItem(1, "点击", IProcessType.OPERATE);
            IProcessItem thiPro = new IProcessItem(2, "输入", IProcessType.OPERATE);
            IProcessItem fouPro = new IProcessItem(3, "点击", IProcessType.OPERATE);
            IProcessItem fifPro = new IProcessItem(4, "收集并解析所有行数据", IProcessType.READ, this.GetResult);

            searchKey = string.IsNullOrEmpty(replaceText) ? searchKey : searchKey.Replace(replaceText, "");
            firPro.JScript = this.SearchMainPage;
            secPro.JScript = "let trackTab = document.getElementById('quick-action_tracking-box');trackTab.click();";
            thiPro.JScript = "let input = document.querySelector('textarea');if(input){input.focus(); document.execCommand('insertText', false, '" + searchKey + "');}";
            fouPro.JScript = "var buttons = document.querySelectorAll('button');" +
                        "buttons.forEach(function(button) {" +
                        "button.classList.forEach(function(clazz){" +
                        "if(clazz.includes('TrackInputAreaContent_track-button')){" +
                        "button.click();" +
                        "return" +
                        "}" +
                        "})" +
                        "});";

            // 依次点击所有行并收集详细数据，然后返回结果
            fifPro.JScript = @"(function() {
                        const iframe = document.getElementById('IframeCurrentEcom');
                        if (!iframe) {
                            return '';
                        }

                        const iframeDocument = iframe.contentDocument || iframe.contentWindow.document;
                        const table = iframeDocument.getElementById('main-grid');

                        if (!table || table.rows.length < 2) {
                            return '';
                        }

                        let allRowsData = [];
                        let currentRowIndex = 1; // 从第一行开始（跳过表头）
                        const totalRows = table.rows.length;
                        let isCompleted = false;

                        function processRowSync(rowIndex) {
                            if (rowIndex >= totalRows) {
                                isCompleted = true;
                                return;
                            }

                            const row = table.rows[rowIndex];
                            const link = row.querySelector('a');

                            if (link) {
                                // 点击链接
                                link.click();

                                // 同步等待详情加载
                                let waitCount = 0;
                                const maxWait = 20; // 最多等待10秒

                                function waitForDetail() {
                                    const detailTable = iframeDocument.getElementById('detail');
                                    if (detailTable) {
                                        const rows = detailTable.querySelectorAll('tbody tr');
                                        if (rows.length > 0) {
                                            // 收集当前行的详细数据
                                            const rowData = {
                                                rowIndex: rowIndex,
                                                detailHtml: detailTable.outerHTML,
                                                timestamp: new Date().toISOString()
                                            };
                                            allRowsData.push(rowData);

                                            // 处理下一行
                                            setTimeout(() => processRowSync(rowIndex + 1), 500);
                                            return;
                                        }
                                    }

                                    waitCount++;
                                    if (waitCount < maxWait) {
                                        setTimeout(waitForDetail, 500);
                                    } else {
                                        // 超时，跳到下一行
                                        setTimeout(() => processRowSync(rowIndex + 1), 100);
                                    }
                                }

                                waitForDetail();
                            } else {
                                // 如果没有链接，跳到下一行
                                setTimeout(() => processRowSync(rowIndex + 1), 100);
                            }
                        }

                        // 开始处理第一行
                        processRowSync(1);

                        // 等待所有行处理完成
                        let checkCount = 0;
                        const maxCheck = 120; // 最多等待60秒

                        function waitForCompletion() {
                            if (isCompleted || checkCount >= maxCheck) {
                                return JSON.stringify(allRowsData);
                            }
                            checkCount++;
                            setTimeout(waitForCompletion, 500);
                        }

                        return waitForCompletion();
                    })()";

            processList.Add(firPro);
            processList.Add(secPro);
            processList.Add(thiPro);
            processList.Add(fouPro);
            processList.Add(fifPro);
            processList.Add(sixPro);

            return processList;
        }

        private IResult GetResult(string resultString)
        {
            try
            {
                IResult result = new IResult();
                try
                {
                    result.ETA = this.GetETA(resultString);
                }
                catch (Exception exc)
                {
                    result.ETAExc = exc.Message;
                }
                try
                {
                    result.ContainerList = this.GetContainerItems(resultString);
                }
                catch (Exception)
                {

                    throw;
                }

                return result;
            }
            catch (Exception exc)
            {
                throw;
            }
        }


        private string GetETA(string result)
        {
            try
            {
                string contentPattern = @"<table[^>]*id=""[^""]*sailing""[^>]*>(.*?)<\/table>";

                MatchCollection matches = Regex.Matches(result, contentPattern, RegexOptions.Singleline);

                if (matches.Count <= 0)
                    throw new Exception("未找到正确的匹配对象");

                string matchVal = matches[0].Value;

                string trPattern = @"<tr>(.*?)<\/tr>";

                MatchCollection trMatches = Regex.Matches(matchVal, trPattern, RegexOptions.Singleline);

                if (trMatches.Count < 2)
                    throw new Exception("未找到正确的匹配对象");

                matchVal = trMatches[trMatches.Count - 1].Value;

                string tdPattern = @"<td[^>]*>(.*?)<\/td>";

                MatchCollection tdMatches = Regex.Matches(matchVal, tdPattern, RegexOptions.Singleline);

                if (tdMatches.Count < 5)
                    throw new Exception("未找到正确的匹配对象");

                matchVal = tdMatches[4].Value;
                string etaPattern = @"<\/span>(.*?)<\/td>";

                MatchCollection etaMatches = Regex.Matches(matchVal, etaPattern, RegexOptions.Singleline);

                if (etaMatches.Count <= 0 || etaMatches[0].Groups.Count < 2)
                    throw new Exception("未找到正确的匹配对象");

                string etaValue = etaMatches[0].Groups[1].Value;

                return this.ParseExactTime(etaValue);

            }
            catch (Exception exc)
            {
                throw;
            }
        }

        private string ParseExactTime(string inputDate)
        {
            try
            {
                if (DateTime.TryParse(inputDate, out DateTime result))
                {
                    return result.ToString("yyyy-MM-dd");
                }
                else
                {
                    throw new Exception("解析日期失败【" + inputDate + "】");
                }

            }
            catch (Exception exc)
            {
                throw;
            }
        }



        private BindingList<IResultContainer> GetContainerItems(string containerString)
        {
            try
            {
                BindingList<IResultContainer> containerItemList = new BindingList<IResultContainer>();

                // 首先尝试解析JSON格式的所有行数据
                if (this.TryParseAllRowsData(containerString, containerItemList))
                {
                    return containerItemList;
                }

                // 如果JSON解析失败，回退到原来的单行解析方式
                string detailTablePattern = @"<table[^>]*id=""detail""[^>]*>(.*?)<\/table>";
                MatchCollection tableMatches = Regex.Matches(containerString, detailTablePattern, RegexOptions.Singleline);

                if (tableMatches.Count > 0)
                {
                    string tableContent = tableMatches[0].Groups[1].Value;
                    this.ParseSingleDetailTable(tableContent, containerItemList);
                }

                // 如果没有解析到任何数据，添加一个空的容器项
                if (containerItemList.Count == 0)
                {
                    IResultContainer containerItem = new IResultContainer();
                    containerItemList.Add(containerItem);
                }

                return containerItemList;
            }
            catch (Exception)
            {
                throw;
            }
        }

        private bool TryParseAllRowsData(string containerString, BindingList<IResultContainer> containerItemList)
        {
            try
            {
                // 检查是否包含JSON数据
                if (!containerString.Contains("all-rows-data") || !containerString.Contains("[{"))
                {
                    return false;
                }

                // 提取JSON数据
                string jsonPattern = @"\[{.*?}\]";
                MatchCollection jsonMatches = Regex.Matches(containerString, jsonPattern, RegexOptions.Singleline);

                if (jsonMatches.Count == 0)
                {
                    return false;
                }

                string jsonData = jsonMatches[0].Value;

                // 解析JSON（简单的手动解析，避免依赖Newtonsoft.Json）
                string[] rowDataItems = this.ExtractJsonObjects(jsonData);

                string containerNo = this.ExtractContainerNumber(containerString);
                string unloadingTime = "";
                string pickupTime = "";
                string returnTime = "";

                foreach (string rowDataItem in rowDataItems)
                {
                    string detailHtml = this.ExtractJsonValue(rowDataItem, "detailHtml");
                    int rowIndex = this.ExtractJsonIntValue(rowDataItem, "rowIndex");

                    if (!string.IsNullOrEmpty(detailHtml))
                    {
                        // 解析每行的详细HTML
                        this.ParseDetailHtmlForRow(detailHtml, rowIndex, containerItemList, ref unloadingTime, ref pickupTime, ref returnTime);
                    }
                }

                // 添加汇总信息
                if (!string.IsNullOrEmpty(unloadingTime) || !string.IsNullOrEmpty(pickupTime) || !string.IsNullOrEmpty(returnTime))
                {
                    IResultContainer summaryItem = new IResultContainer();
                    summaryItem.SetNewContainerItem(
                        string.IsNullOrEmpty(containerNo) ? "汇总信息" : containerNo,
                        pickupTime,
                        unloadingTime,
                        returnTime
                    );
                    containerItemList.Add(summaryItem);
                }

                return true;
            }
            catch
            {
                return false;
            }
        }

        private void ParseSingleDetailTable(string tableContent, BindingList<IResultContainer> containerItemList)
        {
            // 解析表格行
            string rowPattern = @"<tr>(.*?)<\/tr>";
            MatchCollection rowMatches = Regex.Matches(tableContent, rowPattern, RegexOptions.Singleline);

            foreach (Match rowMatch in rowMatches)
            {
                string rowContent = rowMatch.Groups[1].Value;

                // 跳过表头行
                if (rowContent.Contains("<th>")) continue;

                // 解析单元格
                string cellPattern = @"<td[^>]*>(.*?)<\/td>";
                MatchCollection cellMatches = Regex.Matches(rowContent, cellPattern, RegexOptions.Singleline);

                if (cellMatches.Count >= 4)
                {
                    IResultContainer rowItem = new IResultContainer();

                    string no = this.CleanHtmlText(cellMatches[0].Groups[1].Value);
                    string status = this.CleanHtmlText(cellMatches[1].Groups[1].Value);
                    string location = this.CleanHtmlText(cellMatches[2].Groups[1].Value);
                    string dateText = this.CleanHtmlText(cellMatches[3].Groups[1].Value);
                    string parsedDate = this.ParseTrackingDate(dateText);

                    string rowInfo = $"#{no}: {status}";

                    rowItem.SetNewContainerItem(
                        rowInfo.Length > 50 ? rowInfo.Substring(0, 50) : rowInfo,
                        parsedDate,
                        location.Length > 50 ? location.Substring(0, 50) : location,
                        ""
                    );

                    containerItemList.Add(rowItem);
                }
            }
        }

        private void ParseDetailHtmlForRow(string detailHtml, int rowIndex, BindingList<IResultContainer> containerItemList,
            ref string unloadingTime, ref string pickupTime, ref string returnTime)
        {
            // 解析详细HTML中的表格行
            string rowPattern = @"<tr>(.*?)<\/tr>";
            MatchCollection rowMatches = Regex.Matches(detailHtml, rowPattern, RegexOptions.Singleline);

            foreach (Match rowMatch in rowMatches)
            {
                string rowContent = rowMatch.Groups[1].Value;

                // 跳过表头行
                if (rowContent.Contains("<th>")) continue;

                // 解析单元格
                string cellPattern = @"<td[^>]*>(.*?)<\/td>";
                MatchCollection cellMatches = Regex.Matches(rowContent, cellPattern, RegexOptions.Singleline);

                if (cellMatches.Count >= 4)
                {
                    IResultContainer rowItem = new IResultContainer();

                    string no = this.CleanHtmlText(cellMatches[0].Groups[1].Value);
                    string status = this.CleanHtmlText(cellMatches[1].Groups[1].Value);
                    string location = this.CleanHtmlText(cellMatches[2].Groups[1].Value);
                    string dateText = this.CleanHtmlText(cellMatches[3].Groups[1].Value);
                    string parsedDate = this.ParseTrackingDate(dateText);

                    // 根据状态判断关键时间类型
                    if (status.Contains("Unloaded") && status.Contains("Port of Discharging"))
                    {
                        unloadingTime = parsedDate;
                    }
                    else if (status.Contains("Gate Out") && status.Contains("Delivery"))
                    {
                        pickupTime = parsedDate;
                    }
                    else if (status.Contains("Empty Container Returned"))
                    {
                        returnTime = parsedDate;
                    }

                    string rowInfo = $"R{rowIndex}-#{no}: {status}";

                    rowItem.SetNewContainerItem(
                        rowInfo.Length > 50 ? rowInfo.Substring(0, 50) : rowInfo,
                        parsedDate,
                        location.Length > 50 ? location.Substring(0, 50) : location,
                        ""
                    );

                    containerItemList.Add(rowItem);
                }
            }
        }

        private string ExtractContainerNumber(string htmlContent)
        {
            try
            {
                // 尝试从页面中提取集装箱号
                // 通常集装箱号格式为 4个字母 + 7个数字，如 ABCD1234567
                string containerPattern = @"[A-Z]{4}\d{7}";
                MatchCollection matches = Regex.Matches(htmlContent, containerPattern);

                if (matches.Count > 0)
                {
                    return matches[0].Value;
                }

                return "";
            }
            catch
            {
                return "";
            }
        }

        private string[] ExtractJsonObjects(string jsonData)
        {
            try
            {
                // 简单的JSON对象提取，查找 {....} 模式
                List<string> objects = new List<string>();
                int braceCount = 0;
                int startIndex = -1;

                for (int i = 0; i < jsonData.Length; i++)
                {
                    if (jsonData[i] == '{')
                    {
                        if (braceCount == 0)
                        {
                            startIndex = i;
                        }
                        braceCount++;
                    }
                    else if (jsonData[i] == '}')
                    {
                        braceCount--;
                        if (braceCount == 0 && startIndex >= 0)
                        {
                            objects.Add(jsonData.Substring(startIndex, i - startIndex + 1));
                            startIndex = -1;
                        }
                    }
                }

                return objects.ToArray();
            }
            catch
            {
                return new string[0];
            }
        }

        private string ExtractJsonValue(string jsonObject, string key)
        {
            try
            {
                string pattern = $@"""{key}""\s*:\s*""([^""]*)""";
                Match match = Regex.Match(jsonObject, pattern);
                if (match.Success)
                {
                    return match.Groups[1].Value;
                }
                return "";
            }
            catch
            {
                return "";
            }
        }

        private int ExtractJsonIntValue(string jsonObject, string key)
        {
            try
            {
                string pattern = $@"""{key}""\s*:\s*(\d+)";
                Match match = Regex.Match(jsonObject, pattern);
                if (match.Success && int.TryParse(match.Groups[1].Value, out int result))
                {
                    return result;
                }
                return 0;
            }
            catch
            {
                return 0;
            }
        }

        private string CleanHtmlText(string htmlText)
        {
            if (string.IsNullOrEmpty(htmlText)) return "";

            // 移除HTML标签
            string cleaned = Regex.Replace(htmlText, @"<[^>]+>", " ");
            // 移除多余的空白字符
            cleaned = Regex.Replace(cleaned, @"\s+", " ");
            return cleaned.Trim();
        }

        private string ParseTrackingDate(string dateText)
        {
            try
            {
                if (string.IsNullOrEmpty(dateText)) return "";

                // 移除 "Actual" 或 "Estimate" 标记
                dateText = Regex.Replace(dateText, @"(Actual|Estimate)\s*", "");

                // 尝试解析日期
                if (DateTime.TryParse(dateText.Trim(), out DateTime result))
                {
                    return result.ToString("yyyy-MM-dd HH:mm");
                }

                return dateText.Trim();
            }
            catch
            {
                return dateText;
            }
        }


    }
}
