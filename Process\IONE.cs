using HIH.Framework.AutoCrawingManager.Result;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace HIH.Framework.AutoCrawingManager.Process
{
    public class IONE : IProcess
    {
        private readonly string SearchMainPage = "https://ecomm.one-line.com/one-ecom";

        public IONE():base("ONE","ONEY"){}


        public override List<IProcessItem> Run(string searchKey,string replaceText)
        {
            List<IProcessItem> processList = new List<IProcessItem>();

            IProcessItem firPro = new IProcessItem(0, "跳转", IProcessType.JUMPTO);
            IProcessItem secPro = new IProcessItem(1, "点击", IProcessType.OPERATE);
            IProcessItem thiPro = new IProcessItem(2, "输入", IProcessType.OPERATE);
            IProcessItem fouPro = new IProcessItem(3, "点击", IProcessType.OPERATE);
            IProcessItem fifPro = new IProcessItem(4, "等待用户点击集装箱", IProcessType.READ, this.WaitForUserClick);
            IProcessItem sixPro = new IProcessItem(5, "解析集装箱详细数据", IProcessType.READ, this.GetResult);

            searchKey = string.IsNullOrEmpty(replaceText) ? searchKey : searchKey.Replace(replaceText, "");
            firPro.JScript = this.SearchMainPage;
            secPro.JScript = "let trackTab = document.getElementById('quick-action_tracking-box');trackTab.click();";
            thiPro.JScript = "let input = document.querySelector('textarea');if(input){input.focus(); document.execCommand('insertText', false, '" + searchKey + "');}";
            fouPro.JScript = "var buttons = document.querySelectorAll('button');" +
                        "buttons.forEach(function(button) {" +
                        "button.classList.forEach(function(clazz){" +
                        "if(clazz.includes('TrackInputAreaContent_track-button')){" +
                        "button.click();" +
                        "return" +
                        "}" +
                        "})" +
                        "});";
            fifPro.JScript = @"(function() {
                        const iframe = document.getElementById('IframeCurrentEcom');
                        if (!iframe) {
                            return '';
                        }

                        const iframeDocument = iframe.contentDocument || iframe.contentWindow.document;

                        // 检查是否有详细信息表格
                        const detailTable = iframeDocument.getElementById('detail');
                        if (detailTable) {
                            const rows = detailTable.querySelectorAll('tbody tr');
                            if (rows.length > 0) {
                                // 检查是否有实际的追踪数据
                                let hasData = false;
                                for (let i = 0; i < rows.length; i++) {
                                    const cells = rows[i].querySelectorAll('td');
                                    if (cells.length >= 4) {
                                        const statusText = cells[1].textContent.trim();
                                        const dateText = cells[3].textContent.trim();
                                        if (statusText && (dateText || statusText.includes('Empty'))) {
                                            hasData = true;
                                            break;
                                        }
                                    }
                                }

                                if (hasData) {
                                    // 找到详细数据，返回成功标记
                                    console.log('检测到用户已点击集装箱，详细信息已加载');
                                    return 'USER_CLICKED';
                                }
                            }
                        }

                        // 显示提示信息给用户
                        const existingTip = iframeDocument.getElementById('click-tip');
                        if (!existingTip) {
                            const tip = iframeDocument.createElement('div');
                            tip.id = 'click-tip';
                            tip.style.cssText = 'position: fixed; top: 10px; right: 10px; background: #ff6b6b; color: white; padding: 10px; border-radius: 5px; z-index: 9999; font-size: 14px;';
                            tip.textContent = '请点击一个集装箱号查看详细信息';
                            iframeDocument.body.appendChild(tip);
                        }

                        // 返回空字符串让框架继续等待
                        return '';
                    })()";

            sixPro.JScript = @"(function() {
                        const iframe = document.getElementById('IframeCurrentEcom');
                        if (!iframe) {
                            return '';
                        }

                        const iframeDocument = iframe.contentDocument || iframe.contentWindow.document;

                        // 移除提示信息
                        const tip = iframeDocument.getElementById('click-tip');
                        if (tip) {
                            tip.remove();
                        }

                        // 直接返回页面HTML，因为第5步已经确认详细信息已加载
                        console.log('获取集装箱详细追踪数据');
                        return iframeDocument.documentElement.innerHTML;
                    })()";

            processList.Add(firPro);
            processList.Add(secPro);
            processList.Add(thiPro);
            processList.Add(fouPro);
            processList.Add(fifPro);
            processList.Add(sixPro);

            return processList;
        }

        private IResult WaitForUserClick(string resultString)
        {
            try
            {
                IResult result = new IResult();

                // 检查是否检测到用户点击
                if (resultString.Contains("USER_CLICKED"))
                {
                    result.ETA = "用户已点击集装箱，详细信息已加载";
                    result.ContainerList = new BindingList<IResultContainer>();
                }
                else
                {
                    // 用户还没点击，返回状态-1让框架重试
                    result.Status = -1;
                    result.ETA = "等待用户点击集装箱号...";
                    result.ContainerList = new BindingList<IResultContainer>();
                }

                return result;
            }
            catch (Exception)
            {
                throw;
            }
        }

        private IResult GetResult(string resultString)
        {
            try
            {
                IResult result = new IResult();
                string cleanedString = resultString.Trim('\"');

                // 检查清理后的字符串是否仍为空
                if (string.IsNullOrWhiteSpace(cleanedString))
                {
                    result.Status = -1; // 返回-1表示需要重试
                    return result;
                }
               
                try
                {
                    result.ETA = this.GetETA(resultString);
                }
                catch (Exception exc)
                {
                    result.ETAExc = exc.Message;
                }
                try
                {
                    result.ContainerList = this.GetContainerItems(resultString);
                }
                catch (Exception)
                {

                    throw;
                }

                return result;
            }
            catch (Exception exc)
            {
                throw;
            }
        }


        private string GetETA(string result)
        {
            try
            {
                string contentPattern = @"<table[^>]*id=""[^""]*sailing""[^>]*>(.*?)<\/table>";

                MatchCollection matches = Regex.Matches(result, contentPattern, RegexOptions.Singleline);

                if (matches.Count <= 0)
                    throw new Exception("未找到正确的匹配对象");

                string matchVal = matches[0].Value;

                string trPattern = @"<tr>(.*?)<\/tr>";

                MatchCollection trMatches = Regex.Matches(matchVal, trPattern, RegexOptions.Singleline);

                if (trMatches.Count < 2)
                    throw new Exception("未找到正确的匹配对象");

                matchVal = trMatches[trMatches.Count - 1].Value;

                string tdPattern = @"<td[^>]*>(.*?)<\/td>";

                MatchCollection tdMatches = Regex.Matches(matchVal, tdPattern, RegexOptions.Singleline);

                if (tdMatches.Count < 5)
                    throw new Exception("未找到正确的匹配对象");

                matchVal = tdMatches[4].Value;
                string etaPattern = @"<\/span>(.*?)<\/td>";

                MatchCollection etaMatches = Regex.Matches(matchVal, etaPattern, RegexOptions.Singleline);

                if (etaMatches.Count <= 0 || etaMatches[0].Groups.Count < 2)
                    throw new Exception("未找到正确的匹配对象");

                string etaValue = etaMatches[0].Groups[1].Value;

                return this.ParseExactTime(etaValue);

            }
            catch (Exception exc)
            {
                throw;
            }
        }

        private string ParseExactTime(string inputDate)
        {
            try
            {
                if (DateTime.TryParse(inputDate, out DateTime result))
                {
                    return result.ToString("yyyy-MM-dd");
                }
                else
                {
                    throw new Exception("解析日期失败【" + inputDate + "】");
                }

            }
            catch (Exception exc)
            {
                throw;
            }
        }



        private BindingList<IResultContainer> GetContainerItems(string containerString)
        {
            try
            {
                BindingList<IResultContainer> containerItemList = new BindingList<IResultContainer>();

                // 解析详细追踪表格
                string detailTablePattern = @"<table[^>]*id=""detail""[^>]*>(.*?)<\/table>";
                MatchCollection tableMatches = Regex.Matches(containerString, detailTablePattern, RegexOptions.Singleline);

                if (tableMatches.Count > 0)
                {
                    string tableContent = tableMatches[0].Groups[1].Value;

                    // 解析表格行
                    string rowPattern = @"<tr>(.*?)<\/tr>";
                    MatchCollection rowMatches = Regex.Matches(tableContent, rowPattern, RegexOptions.Singleline);

                    // 用于收集关键时间信息
                    string containerNo = this.ExtractContainerNumberFromPage(containerString);
                    string unloadingTime = "";
                    string pickupTime = "";
                    string returnTime = "";

                    foreach (Match rowMatch in rowMatches)
                    {
                        string rowContent = rowMatch.Groups[1].Value;

                        // 跳过表头行
                        if (rowContent.Contains("<th>")) continue;

                        // 解析单元格
                        string cellPattern = @"<td[^>]*>(.*?)<\/td>";
                        MatchCollection cellMatches = Regex.Matches(rowContent, cellPattern, RegexOptions.Singleline);

                        if (cellMatches.Count >= 4)
                        {
                            // 为每一行创建一个容器项来记录详细信息
                            IResultContainer rowItem = new IResultContainer();

                            string no = this.CleanHtmlText(cellMatches[0].Groups[1].Value);
                            string status = this.CleanHtmlText(cellMatches[1].Groups[1].Value);
                            string location = this.CleanHtmlText(cellMatches[2].Groups[1].Value);
                            string dateText = this.CleanHtmlText(cellMatches[3].Groups[1].Value);
                            string parsedDate = this.ParseTrackingDate(dateText);

                            // 根据状态判断关键时间类型并记录
                            if (status.Contains("Unloaded") && status.Contains("Port of Discharging"))
                            {
                                unloadingTime = parsedDate;
                            }
                            else if (status.Contains("Gate Out") && status.Contains("Delivery"))
                            {
                                pickupTime = parsedDate;
                            }
                            else if (status.Contains("Empty Container Returned"))
                            {
                                returnTime = parsedDate;
                            }

                            // 使用ContainerNo字段存储行号和状态信息
                            // 使用其他时间字段存储位置和日期信息
                            string rowInfo = $"#{no}: {status}";
                            string locationInfo = location;
                            string dateInfo = parsedDate;

                            rowItem.SetNewContainerItem(
                                rowInfo.Length > 50 ? rowInfo.Substring(0, 50) : rowInfo,
                                dateInfo,
                                locationInfo.Length > 50 ? locationInfo.Substring(0, 50) : locationInfo,
                                "" // 暂时不使用返回时间字段
                            );

                            containerItemList.Add(rowItem);
                        }
                    }

                    // 如果找到了关键时间信息，在最后添加一个汇总项
                    if (!string.IsNullOrEmpty(unloadingTime) || !string.IsNullOrEmpty(pickupTime) || !string.IsNullOrEmpty(returnTime))
                    {
                        IResultContainer summaryItem = new IResultContainer();
                        summaryItem.SetNewContainerItem(
                            string.IsNullOrEmpty(containerNo) ? "汇总信息" : containerNo,
                            pickupTime,
                            unloadingTime,
                            returnTime
                        );
                        containerItemList.Add(summaryItem);
                    }
                }

                // 如果没有解析到任何数据，添加一个空的容器项
                if (containerItemList.Count == 0)
                {
                    IResultContainer containerItem = new IResultContainer();
                    containerItemList.Add(containerItem);
                }

                return containerItemList;
            }
            catch (Exception)
            {
                throw;
            }
        }

        private string ExtractContainerNumberFromPage(string htmlContent)
        {
            try
            {
                // 首先尝试从详细信息页面的标题或表格中提取集装箱号
                // 查找包含"Container No."的内容
                string containerNoPattern = @"Container\s+No\.?\s*:?\s*([A-Z]{4}\d{7})";
                MatchCollection containerMatches = Regex.Matches(htmlContent, containerNoPattern, RegexOptions.IgnoreCase);

                if (containerMatches.Count > 0)
                {
                    return containerMatches[0].Groups[1].Value;
                }

                // 如果没找到，尝试通用的集装箱号格式
                // 通常集装箱号格式为 4个字母 + 7个数字，如 ABCD1234567
                string generalPattern = @"[A-Z]{4}\d{7}";
                MatchCollection generalMatches = Regex.Matches(htmlContent, generalPattern);

                if (generalMatches.Count > 0)
                {
                    return generalMatches[0].Value;
                }

                return "";
            }
            catch
            {
                return "";
            }
        }

        private string CleanHtmlText(string htmlText)
        {
            if (string.IsNullOrEmpty(htmlText)) return "";

            // 移除HTML标签
            string cleaned = Regex.Replace(htmlText, @"<[^>]+>", " ");
            // 移除多余的空白字符
            cleaned = Regex.Replace(cleaned, @"\s+", " ");
            return cleaned.Trim();
        }

        private string ParseTrackingDate(string dateText)
        {
            try
            {
                if (string.IsNullOrEmpty(dateText)) return "";

                // 移除 "Actual" 或 "Estimate" 标记
                dateText = Regex.Replace(dateText, @"(Actual|Estimate)\s*", "");

                // 尝试解析日期
                if (DateTime.TryParse(dateText.Trim(), out DateTime result))
                {
                    return result.ToString("yyyy-MM-dd HH:mm");
                }

                return dateText.Trim();
            }
            catch
            {
                return dateText;
            }
        }


    }
}
