using HIH.Framework.AutoCrawingManager.Result;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace HIH.Framework.AutoCrawingManager.Process
{
    public class IONE : IProcess
    {
        private readonly string SearchMainPage = "https://ecomm.one-line.com/one-ecom";

        public IONE():base("ONE","ONEY"){}


        public override List<IProcessItem> Run(string searchKey,string replaceText)
        {
            List<IProcessItem> processList = new List<IProcessItem>();

            IProcessItem firPro = new IProcessItem(0, "跳转", IProcessType.JUMPTO);
            IProcessItem secPro = new IProcessItem(1, "点击", IProcessType.OPERATE);
            IProcessItem thiPro = new IProcessItem(2, "输入", IProcessType.OPERATE);
            IProcessItem fouPro = new IProcessItem(3, "点击", IProcessType.OPERATE);
            IProcessItem fifPro = new IProcessItem(4, "自动处理所有集装箱", IProcessType.READ, this.ProcessAllContainers);
            IProcessItem sixPro = new IProcessItem(5, "解析集装箱详细数据", IProcessType.READ, this.GetResult);

            searchKey = string.IsNullOrEmpty(replaceText) ? searchKey : searchKey.Replace(replaceText, "");
            firPro.JScript = this.SearchMainPage;
            secPro.JScript = "let trackTab = document.getElementById('quick-action_tracking-box');trackTab.click();";
            thiPro.JScript = "let input = document.querySelector('textarea');if(input){input.focus(); document.execCommand('insertText', false, '" + searchKey + "');}";
            fouPro.JScript = "var buttons = document.querySelectorAll('button');" +
                        "buttons.forEach(function(button) {" +
                        "button.classList.forEach(function(clazz){" +
                        "if(clazz.includes('TrackInputAreaContent_track-button')){" +
                        "button.click();" +
                        "return" +
                        "}" +
                        "})" +
                        "});";
            fifPro.JScript = @"(function() {
                        return new Promise((resolve) => {
                            const iframe = document.getElementById('IframeCurrentEcom');
                            if (!iframe) {
                                resolve(JSON.stringify([]));
                                return;
                            }

                            const iframeDocument = iframe.contentDocument || iframe.contentWindow.document;
                            const mainTable = iframeDocument.getElementById('main-grid');

                            if (!mainTable || mainTable.rows.length <= 1) {
                                resolve(JSON.stringify([]));
                                return;
                            }

                            let allContainerData = [];
                            let currentIndex = 0;
                            const totalRows = mainTable.rows.length - 1; // 减去表头

                            // 显示进度提示
                            const tip = iframeDocument.createElement('div');
                            tip.id = 'auto-progress-tip';
                            tip.style.cssText = 'position: fixed; top: 10px; right: 10px; background: #4CAF50; color: white; padding: 15px; border-radius: 8px; z-index: 9999; font-size: 16px; box-shadow: 0 4px 8px rgba(0,0,0,0.3);';
                            iframeDocument.body.appendChild(tip);

                            function processNextContainer() {
                                if (currentIndex >= totalRows) {
                                    // 所有集装箱都处理完了
                                    tip.textContent = '✅ 所有集装箱处理完成！';
                                    tip.style.background = '#2196F3';

                                    setTimeout(() => {
                                        resolve(JSON.stringify(allContainerData));
                                    }, 1000);
                                    return;
                                }

                                const rowIndex = currentIndex + 1; // +1 因为要跳过表头
                                const row = mainTable.rows[rowIndex];
                                const link = row.querySelector('a');

                                if (!link) {
                                    // 没有链接，跳到下一个
                                    currentIndex++;
                                    setTimeout(processNextContainer, 100);
                                    return;
                                }

                                const containerNo = link.textContent.trim();
                                tip.textContent = `🚢 正在处理集装箱 ${currentIndex + 1}/${totalRows}: ${containerNo}`;

                                // 点击链接
                                link.click();

                                // 等待详细信息加载
                                setTimeout(() => {
                                    waitForDetailAndCollect(containerNo);
                                }, 2000);
                            }

                            function waitForDetailAndCollect(containerNo) {
                                const detailTable = iframeDocument.getElementById('detail');
                                if (detailTable) {
                                    const rows = detailTable.querySelectorAll('tbody tr');
                                    if (rows.length > 0) {
                                        // 检查是否有实际的追踪数据
                                        let hasData = false;
                                        for (let i = 0; i < rows.length; i++) {
                                            const cells = rows[i].querySelectorAll('td');
                                            if (cells.length >= 4) {
                                                const statusText = cells[1].textContent.trim();
                                                const dateText = cells[3].textContent.trim();
                                                if (statusText && (dateText || statusText.includes('Empty'))) {
                                                    hasData = true;
                                                    break;
                                                }
                                            }
                                        }

                                        if (hasData) {
                                            // 收集当前集装箱的详细数据
                                            const containerData = {
                                                containerNo: containerNo,
                                                index: currentIndex,
                                                detailHtml: detailTable.outerHTML,
                                                timestamp: new Date().toISOString()
                                            };
                                            allContainerData.push(containerData);

                                            // 处理下一个集装箱
                                            currentIndex++;
                                            setTimeout(processNextContainer, 1000);
                                            return;
                                        }
                                    }
                                }

                                // 如果详细信息还没加载完，继续等待
                                setTimeout(() => waitForDetailAndCollect(containerNo), 1000);
                            }

                            // 开始处理第一个集装箱
                            processNextContainer();
                        });
                    })()";

            sixPro.JScript = @"(function() {
                        const iframe = document.getElementById('IframeCurrentEcom');
                        if (!iframe) {
                            return '';
                        }

                        const iframeDocument = iframe.contentDocument || iframe.contentWindow.document;

                        // 移除提示信息
                        const tip = iframeDocument.getElementById('click-tip');
                        if (tip) {
                            tip.remove();
                        }

                        // 直接返回页面HTML，因为第5步已经确认详细信息已加载
                        console.log('获取集装箱详细追踪数据');
                        return iframeDocument.documentElement.innerHTML;
                    })()";

            processList.Add(firPro);
            processList.Add(secPro);
            processList.Add(thiPro);
            processList.Add(fouPro);
            processList.Add(fifPro);
            processList.Add(sixPro);

            return processList;
        }

        private IResult ProcessAllContainers(string resultString)
        {
            try
            {
                IResult result = new IResult();

                // 清空之前的数据
                _allContainerData.Clear();

                // 解析JSON数据
                if (!string.IsNullOrEmpty(resultString) && resultString.StartsWith("["))
                {
                    // 简单的JSON解析
                    string[] containerDataItems = this.ExtractJsonObjects(resultString);

                    foreach (string containerDataItem in containerDataItems)
                    {
                        string containerNo = this.ExtractJsonValue(containerDataItem, "containerNo");
                        string detailHtml = this.ExtractJsonValue(containerDataItem, "detailHtml");

                        if (!string.IsNullOrEmpty(containerNo) && !string.IsNullOrEmpty(detailHtml))
                        {
                            // 解析详细HTML
                            this.ParseContainerDetailHtml(containerNo, detailHtml);
                        }
                    }

                    result.ETA = $"成功处理了 {containerDataItems.Length} 个集装箱";
                    result.ContainerList = _allContainerData;
                }
                else
                {
                    result.ETA = "没有找到集装箱数据";
                    result.ContainerList = new BindingList<IResultContainer>();
                }

                return result;
            }
            catch (Exception)
            {
                throw;
            }
        }

        private IResult WaitForUserClick(string resultString)
        {
            try
            {
                IResult result = new IResult();

                // 检查是否所有集装箱都处理完成
                if (resultString.Contains("ALL_COMPLETED"))
                {
                    result.ETA = "所有集装箱都已处理完成";
                    result.ContainerList = new BindingList<IResultContainer>();
                    return result;
                }

                // 检查是否找到了集装箱数据
                if (resultString.Contains("CONTAINER_DATA_"))
                {
                    // 提取当前处理的索引
                    string indexStr = resultString.Replace("CONTAINER_DATA_", "");
                    result.ETA = $"已获取集装箱数据，索引: {indexStr}";
                    result.ContainerList = new BindingList<IResultContainer>();
                    return result;
                }

                // 其他情况继续等待/重试
                result.Status = -1;
                result.ETA = "正在处理集装箱数据...";
                result.ContainerList = new BindingList<IResultContainer>();

                return result;
            }
            catch (Exception)
            {
                throw;
            }
        }

        private IResult GetResult(string resultString)
        {
            try
            {
                IResult result = new IResult();
                string cleanedString = resultString.Trim('\"');

                // 检查清理后的字符串是否仍为空
                if (string.IsNullOrWhiteSpace(cleanedString))
                {
                    result.Status = -1; // 返回-1表示需要重试
                    return result;
                }
               
                try
                {
                    result.ETA = this.GetETA(resultString);
                }
                catch (Exception exc)
                {
                    result.ETAExc = exc.Message;
                }
                try
                {
                    result.ContainerList = this.GetContainerItems(resultString);
                }
                catch (Exception)
                {

                    throw;
                }

                return result;
            }
            catch (Exception exc)
            {
                throw;
            }
        }


        private string GetETA(string result)
        {
            try
            {
                string contentPattern = @"<table[^>]*id=""[^""]*sailing""[^>]*>(.*?)<\/table>";

                MatchCollection matches = Regex.Matches(result, contentPattern, RegexOptions.Singleline);

                if (matches.Count <= 0)
                    throw new Exception("未找到正确的匹配对象");

                string matchVal = matches[0].Value;

                string trPattern = @"<tr>(.*?)<\/tr>";

                MatchCollection trMatches = Regex.Matches(matchVal, trPattern, RegexOptions.Singleline);

                if (trMatches.Count < 2)
                    throw new Exception("未找到正确的匹配对象");

                matchVal = trMatches[trMatches.Count - 1].Value;

                string tdPattern = @"<td[^>]*>(.*?)<\/td>";

                MatchCollection tdMatches = Regex.Matches(matchVal, tdPattern, RegexOptions.Singleline);

                if (tdMatches.Count < 5)
                    throw new Exception("未找到正确的匹配对象");

                matchVal = tdMatches[4].Value;
                string etaPattern = @"<\/span>(.*?)<\/td>";

                MatchCollection etaMatches = Regex.Matches(matchVal, etaPattern, RegexOptions.Singleline);

                if (etaMatches.Count <= 0 || etaMatches[0].Groups.Count < 2)
                    throw new Exception("未找到正确的匹配对象");

                string etaValue = etaMatches[0].Groups[1].Value;

                return this.ParseExactTime(etaValue);

            }
            catch (Exception exc)
            {
                throw;
            }
        }

        private string ParseExactTime(string inputDate)
        {
            try
            {
                if (DateTime.TryParse(inputDate, out DateTime result))
                {
                    return result.ToString("yyyy-MM-dd");
                }
                else
                {
                    throw new Exception("解析日期失败【" + inputDate + "】");
                }

            }
            catch (Exception exc)
            {
                throw;
            }
        }



        // 静态变量用于累积所有集装箱的数据
        private static BindingList<IResultContainer> _allContainerData = new BindingList<IResultContainer>();

        private BindingList<IResultContainer> GetContainerItems(string containerString)
        {
            try
            {
                // 解析详细追踪表格
                string detailTablePattern = @"<table[^>]*id=""detail""[^>]*>(.*?)<\/table>";
                MatchCollection tableMatches = Regex.Matches(containerString, detailTablePattern, RegexOptions.Singleline);

                if (tableMatches.Count > 0)
                {
                    string tableContent = tableMatches[0].Groups[1].Value;

                    // 解析表格行
                    string rowPattern = @"<tr>(.*?)<\/tr>";
                    MatchCollection rowMatches = Regex.Matches(tableContent, rowPattern, RegexOptions.Singleline);

                    // 用于收集关键时间信息
                    string containerNo = this.ExtractContainerNumberFromPage(containerString);
                    string unloadingTime = "";
                    string pickupTime = "";
                    string returnTime = "";

                    // 检查这个集装箱是否已经处理过
                    bool alreadyProcessed = false;
                    if (!string.IsNullOrEmpty(containerNo))
                    {
                        foreach (var existingItem in _allContainerData)
                        {
                            if (existingItem.ContainerNo == containerNo)
                            {
                                alreadyProcessed = true;
                                break;
                            }
                        }
                    }

                    if (!alreadyProcessed && !string.IsNullOrEmpty(containerNo))
                    {
                        foreach (Match rowMatch in rowMatches)
                        {
                            string rowContent = rowMatch.Groups[1].Value;

                            // 跳过表头行
                            if (rowContent.Contains("<th>")) continue;

                            // 解析单元格
                            string cellPattern = @"<td[^>]*>(.*?)<\/td>";
                            MatchCollection cellMatches = Regex.Matches(rowContent, cellPattern, RegexOptions.Singleline);

                            if (cellMatches.Count >= 4)
                            {
                                string no = this.CleanHtmlText(cellMatches[0].Groups[1].Value);
                                string status = this.CleanHtmlText(cellMatches[1].Groups[1].Value);
                                string location = this.CleanHtmlText(cellMatches[2].Groups[1].Value);
                                string dateText = this.CleanHtmlText(cellMatches[3].Groups[1].Value);
                                string parsedDate = this.ParseTrackingDate(dateText);

                                // 根据状态判断关键时间类型并记录
                                if (status.Contains("Unloaded") && status.Contains("Port of Discharging"))
                                {
                                    unloadingTime = parsedDate;
                                }
                                else if (status.Contains("Gate Out") && status.Contains("Delivery"))
                                {
                                    pickupTime = parsedDate;
                                }
                                else if (status.Contains("Empty Container Returned"))
                                {
                                    returnTime = parsedDate;
                                }

                                // 为每一行创建一个容器项来记录详细信息
                                IResultContainer rowItem = new IResultContainer();
                                string rowInfo = $"{containerNo}-#{no}: {status}";

                                rowItem.SetNewContainerItem(
                                    rowInfo.Length > 50 ? rowInfo.Substring(0, 50) : rowInfo,
                                    parsedDate,
                                    location.Length > 50 ? location.Substring(0, 50) : location,
                                    ""
                                );

                                _allContainerData.Add(rowItem);
                            }
                        }

                        // 添加汇总项
                        if (!string.IsNullOrEmpty(unloadingTime) || !string.IsNullOrEmpty(pickupTime) || !string.IsNullOrEmpty(returnTime))
                        {
                            IResultContainer summaryItem = new IResultContainer();
                            summaryItem.SetNewContainerItem(
                                $"{containerNo} 汇总",
                                pickupTime,
                                unloadingTime,
                                returnTime
                            );
                            _allContainerData.Add(summaryItem);
                        }
                    }
                }

                // 返回累积的所有数据
                return _allContainerData;
            }
            catch (Exception)
            {
                throw;
            }
        }

        private void ParseContainerDetailHtml(string containerNo, string detailHtml)
        {
            try
            {
                // 解析表格行
                string rowPattern = @"<tr>(.*?)<\/tr>";
                MatchCollection rowMatches = Regex.Matches(detailHtml, rowPattern, RegexOptions.Singleline);

                string unloadingTime = "";
                string pickupTime = "";
                string returnTime = "";

                foreach (Match rowMatch in rowMatches)
                {
                    string rowContent = rowMatch.Groups[1].Value;

                    // 跳过表头行
                    if (rowContent.Contains("<th>")) continue;

                    // 解析单元格
                    string cellPattern = @"<td[^>]*>(.*?)<\/td>";
                    MatchCollection cellMatches = Regex.Matches(rowContent, cellPattern, RegexOptions.Singleline);

                    if (cellMatches.Count >= 4)
                    {
                        string no = this.CleanHtmlText(cellMatches[0].Groups[1].Value);
                        string status = this.CleanHtmlText(cellMatches[1].Groups[1].Value);
                        string location = this.CleanHtmlText(cellMatches[2].Groups[1].Value);
                        string dateText = this.CleanHtmlText(cellMatches[3].Groups[1].Value);
                        string parsedDate = this.ParseTrackingDate(dateText);

                        // 根据状态判断关键时间类型并记录
                        if (status.Contains("Unloaded") && status.Contains("Port of Discharging"))
                        {
                            unloadingTime = parsedDate;
                        }
                        else if (status.Contains("Gate Out") && status.Contains("Delivery"))
                        {
                            pickupTime = parsedDate;
                        }
                        else if (status.Contains("Empty Container Returned"))
                        {
                            returnTime = parsedDate;
                        }

                        // 为每一行创建一个容器项来记录详细信息
                        IResultContainer rowItem = new IResultContainer();
                        string rowInfo = $"{containerNo}-#{no}: {status}";

                        rowItem.SetNewContainerItem(
                            rowInfo.Length > 50 ? rowInfo.Substring(0, 50) : rowInfo,
                            parsedDate,
                            location.Length > 50 ? location.Substring(0, 50) : location,
                            ""
                        );

                        _allContainerData.Add(rowItem);
                    }
                }

                // 添加汇总项
                if (!string.IsNullOrEmpty(unloadingTime) || !string.IsNullOrEmpty(pickupTime) || !string.IsNullOrEmpty(returnTime))
                {
                    IResultContainer summaryItem = new IResultContainer();
                    summaryItem.SetNewContainerItem(
                        $"{containerNo} 汇总",
                        pickupTime,
                        unloadingTime,
                        returnTime
                    );
                    _allContainerData.Add(summaryItem);
                }
            }
            catch
            {
                // 忽略解析错误
            }
        }

        private string ExtractContainerNumberFromPage(string htmlContent)
        {
            try
            {
                // 首先尝试从详细信息页面的标题或表格中提取集装箱号
                // 查找包含"Container No."的内容
                string containerNoPattern = @"Container\s+No\.?\s*:?\s*([A-Z]{4}\d{7})";
                MatchCollection containerMatches = Regex.Matches(htmlContent, containerNoPattern, RegexOptions.IgnoreCase);

                if (containerMatches.Count > 0)
                {
                    return containerMatches[0].Groups[1].Value;
                }

                // 如果没找到，尝试通用的集装箱号格式
                // 通常集装箱号格式为 4个字母 + 7个数字，如 ABCD1234567
                string generalPattern = @"[A-Z]{4}\d{7}";
                MatchCollection generalMatches = Regex.Matches(htmlContent, generalPattern);

                if (generalMatches.Count > 0)
                {
                    return generalMatches[0].Value;
                }

                return "";
            }
            catch
            {
                return "";
            }
        }

        private string CleanHtmlText(string htmlText)
        {
            if (string.IsNullOrEmpty(htmlText)) return "";

            // 移除HTML标签
            string cleaned = Regex.Replace(htmlText, @"<[^>]+>", " ");
            // 移除多余的空白字符
            cleaned = Regex.Replace(cleaned, @"\s+", " ");
            return cleaned.Trim();
        }

        private string ParseTrackingDate(string dateText)
        {
            try
            {
                if (string.IsNullOrEmpty(dateText)) return "";

                // 移除 "Actual" 或 "Estimate" 标记
                dateText = Regex.Replace(dateText, @"(Actual|Estimate)\s*", "");

                // 尝试解析日期
                if (DateTime.TryParse(dateText.Trim(), out DateTime result))
                {
                    return result.ToString("yyyy-MM-dd HH:mm");
                }

                return dateText.Trim();
            }
            catch
            {
                return dateText;
            }
        }

        private string[] ExtractJsonObjects(string jsonData)
        {
            try
            {
                // 简单的JSON对象提取，查找 {....} 模式
                List<string> objects = new List<string>();
                int braceCount = 0;
                int startIndex = -1;

                for (int i = 0; i < jsonData.Length; i++)
                {
                    if (jsonData[i] == '{')
                    {
                        if (braceCount == 0)
                        {
                            startIndex = i;
                        }
                        braceCount++;
                    }
                    else if (jsonData[i] == '}')
                    {
                        braceCount--;
                        if (braceCount == 0 && startIndex >= 0)
                        {
                            objects.Add(jsonData.Substring(startIndex, i - startIndex + 1));
                            startIndex = -1;
                        }
                    }
                }

                return objects.ToArray();
            }
            catch
            {
                return new string[0];
            }
        }

        private string ExtractJsonValue(string jsonObject, string key)
        {
            try
            {
                string pattern = $@"""{key}""\s*:\s*""([^""]*)""";
                Match match = Regex.Match(jsonObject, pattern);
                if (match.Success)
                {
                    return match.Groups[1].Value;
                }
                return "";
            }
            catch
            {
                return "";
            }
        }


    }
}
